<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>NDI SDK</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: NDI SDK
    Version: 5.6.1-->
<Location>Engine/Plugins/Media/NDIMedia/Source/ThirdParty/NDI</Location>
  <Function>Integrated with the Media Framework in Unreal Engine.</Function>
  <Eula>https://airtable.com/apppjdGdtE9240uU7/tblyCaviAYfIl3OQm/viwPNScZFkLzjzQ1V/recZ5C0A3LNCgcvXj/fldRfBBmhWETfFNY8/attd7RjcuFdVPX5So?blocks=hide
</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 