// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once 

#include "ChaosClothAsset/SimulationBaseConfigNode.h"
#include "ChaosClothAsset/WeightedValue.h"
#include "SimulationXPBDEdgeSpringConfigNode.generated.h"

/** XPBD edge spring constraint property configuration node. */
USTRUCT(Meta = (DataflowCloth, Deprecated = "5.2"))
struct FChaosClothAssetSimulationXPBDEdgeSpringConfigNode : public FChaosClothAssetSimulationBaseConfigNode
{
	GENERATED_USTRUCT_BODY()
	DATAFLOW_NODE_DEFINE_INTERNAL(FChaosClothAssetSimulationXPBDEdgeSpringConfigNode, "SimulationXPBDEdgeSpringConfig", "Cloth", "Cloth Simulation XPBD Edge Spring Config")

public:
	/**
	 * The stiffness of the edge segment constraints.
	 * If a valid weight map is found with the given Weight Map name, then both Low and High values
	 * are interpolated with the per particle weight to make the final value used for the simulation.
	 * Otherwise all particles are considered to have a zero weight, and only the Low value is meaningful.
	 */
	UPROPERTY(EditAnywhere, Category = "XPBDEdgeSpring Properties", Meta = (UIMin = "0", UIMax = "10000", ClampMin = "0", ClampMax = "1000000000"))
	FChaosClothAssetWeightedValue XPBDEdgeSpringStiffness = { true, 100.f, 100.f, TEXT("XPBDEdgeSpringStiffness") };

	/**
	 * The damping of the edge segment constraints.
	 * If a valid weight map is found with the given Weight Map name, then both Low and High values
	 * are interpolated with the per particle weight to make the final value used for the simulation.
	 * Otherwise all particles are considered to have a zero weight, and only the Low value is meaningful.
	 */
	UPROPERTY(EditAnywhere, Category = "XPBDEdgeSpring Properties", Meta = (UIMin = "0", UIMax = "10", ClampMin = "0", ClampMax = "1000"))
	FChaosClothAssetWeightedValue XPBDEdgeSpringDamping = { true, 1.f, 1.f, TEXT("XPBDEdgeSpringDamping") };

	FChaosClothAssetSimulationXPBDEdgeSpringConfigNode(const UE::Dataflow::FNodeParameters& InParam, FGuid InGuid = FGuid::NewGuid());

private:
	virtual void AddProperties(FPropertyHelper& PropertyHelper) const override;
};
