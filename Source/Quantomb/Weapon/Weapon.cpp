// Copyright © 2025 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.


#include "Weapon.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/PlayerController.h"
#include "Camera/PlayerCameraManager.h"
#include "GameFramework/Pawn.h"
#include "Quantomb/Player/QuantombPlayer.h"
#include "WeaponComponent/WeaponComponentSight/WeaponComponentSight.h"


// Sets default values
AWeapon::AWeapon()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AWeapon::BeginPlay()
{
	Super::BeginPlay();

}

// Called every frame
void AWeapon::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

// Gets transform of aim point from component of either type IronSights or Scope
AWeaponComponent* AWeapon::GetWeaponComponentByType(EWeaponComponentType WeaponComponentType)
{
	// Get all attached actors and filter for weapon components
	TArray<AActor*> AttachedActors;
	GetAttachedActors(AttachedActors);

	for (AActor* AttachedActor : AttachedActors)
	{
		if (AWeaponComponent* WeaponComponent = Cast<AWeaponComponent>(AttachedActor))
		{
			if (WeaponComponent->WeaponComponentType == WeaponComponentType)
			{
				return WeaponComponent;
			}
		}
	}
	return nullptr;
}

/**
 * Gets transform that the WeaponControl should be set to for ADS
 * @param WeaponComponentType The type of weapon component to get the aim point from (IronSights or Scope)
 * @return The transform the WeaponControl should be set to
 */
FTransform AWeapon::GetADSAimPointTransform(EWeaponComponentType WeaponComponentType)
{
	// Get the weapon component (iron sights or scope)
	AWeaponComponentSight* WeaponComponentSight = Cast<AWeaponComponentSight>(GetWeaponComponentByType(WeaponComponentType));
	if (!WeaponComponentSight)
	{
		UE_LOG(LogTemp, Warning, TEXT("WeaponComponent not found for type: %d or cast to AWeaponComponentSight failed"), (int32)WeaponComponentType);
		return FTransform::Identity;
	}

	// Get the skeletal mesh component from the weapon component
	USkeletalMeshComponent* SkeletalMeshComponent = WeaponComponentSight->FindComponentByClass<USkeletalMeshComponent>();
	if (!SkeletalMeshComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("SkeletalMeshComponent not found on WeaponComponent"));
		return FTransform::Identity;
	}

	// Find the player controller
	UWorld* World = GetWorld();
	if (!World)
	{
		UE_LOG(LogTemp, Warning, TEXT("World not found"));
		return FTransform::Identity;
	}

	APlayerController* PlayerController = World->GetFirstPlayerController();
	if (!PlayerController)
	{
		UE_LOG(LogTemp, Warning, TEXT("PlayerController not found"));
		return FTransform::Identity;
	}

	// Get the player pawn
	APawn* PlayerPawn = PlayerController->GetPawn();
	if (!PlayerPawn)
	{
		UE_LOG(LogTemp, Warning, TEXT("PlayerPawn not found"));
		return FTransform::Identity;
	}

	// Cast to QuantombPlayer to access WeaponControl
	AQuantombPlayer* QuantombPlayer = Cast<AQuantombPlayer>(PlayerPawn);
	if (!QuantombPlayer)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to cast PlayerPawn to QuantombPlayer"));
		return FTransform::Identity;
	}

	// Find the WeaponControl static mesh component
	UStaticMeshComponent* WeaponControl = QuantombPlayer->FindComponentByClass<UStaticMeshComponent>();
	if (!WeaponControl)
	{
		UE_LOG(LogTemp, Warning, TEXT("WeaponControl StaticMeshComponent not found"));
		return FTransform::Identity;
	}

	// Get camera world transform to convert AimPoint to camera space
	UCameraComponent* CameraComponent = QuantombPlayer->FindComponentByClass<UCameraComponent>();
	if (!CameraComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("CameraComponent not found"));
		return FTransform::Identity;
	}


	// Get the weapon's skeletal mesh component (with origin at root bone center)
	USkeletalMeshComponent* WeaponSkeletalMesh = FindComponentByClass<USkeletalMeshComponent>();
	if (!WeaponSkeletalMesh)
	{
		UE_LOG(LogTemp, Error, TEXT("No skeletal mesh found on weapon!"));
		return FTransform::Identity;
	}

	// Calculate the offset from weapon origin to aimpoint socket
	// This traverses: Weapon Origin -> "ironsights" socket -> WC_IronSights_Default -> "aimpoint" socket
	const FTransform WeaponOriginTransform = WeaponSkeletalMesh->GetComponentTransform();

	// FIXED: Get the aimpoint transform in the weapon component's local space (relative to the weapon component)
	// This ensures the offset is immune to weapon animations since it's calculated in local space first,
	// then properly transformed to camera space. Previously, both transforms were calculated in world space
	// which caused the offset to be affected by weapon animations (like sprinting).
	const FTransform AimPointLocalTransform = SkeletalMeshComponent->GetSocketTransform(FName("aimpoint"), RTS_Component);
	const FVector WeaponOriginToAimPointOffsetLocal = AimPointLocalTransform.GetLocation();

	// Get camera transform for coordinate space conversion
	const FTransform CameraWorldTransform = CameraComponent->GetComponentTransform();

	// Convert weapon origin to camera space
	const FTransform WeaponOriginInCameraSpace = WeaponOriginTransform.GetRelativeTransform(CameraWorldTransform);

	// Transform the local offset vector to camera space using the weapon's orientation in camera space
	// This preserves the relative relationship between weapon origin and aimpoint regardless of weapon animation
	const FVector WeaponOriginToAimPointOffset = WeaponOriginInCameraSpace.TransformVector(WeaponOriginToAimPointOffsetLocal);
	UE_LOG(LogTemp, Warning, TEXT("WeaponOriginToAimPointOffset: %s"), *WeaponOriginToAimPointOffset.ToString());
	UE_LOG(LogTemp, Warning, TEXT("WeaponOriginToAimPointOffsetLocal: %s"), *WeaponOriginToAimPointOffsetLocal.ToString());
	UE_LOG(LogTemp, Warning, TEXT("WeaponOriginInCameraSpace: %s"), *WeaponOriginInCameraSpace.GetLocation().ToString());

	// To center the aimpoint at (0,0,0), move WeaponControl by the negative of this offset
	const FVector TargetWeaponControlLocation = -WeaponOriginToAimPointOffset;

	// Remove any Y and Z offsets
	const FVector TargetWeaponControlLocationZOnly = FVector(0, 0, TargetWeaponControlLocation.Z);

	// Add the components aimpoint distance to the target location
	const FVector TargetWeaponControlLocationAdjusted = TargetWeaponControlLocationZOnly + FVector(WeaponComponentSight->CameraAimPointDistance, 0, 0);

	// Create the target transform (relative to camera)
	const FTransform TargetWeaponControlTransform(FQuat::Identity, TargetWeaponControlLocationAdjusted);

	// Convert to relative transform (relative to the player)
	/*const FTransform PlayerWorldTransform = QuantombPlayer->GetActorTransform();
	const FTransform RelativeWeaponControlTransform = TargetWeaponControlTransform.GetRelativeTransform(PlayerWorldTransform);*/

	// Store for reference
	CurrentADSAimPointTransform = TargetWeaponControlTransform;

	return TargetWeaponControlTransform;
}
