// Copyright © 2024 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.

#pragma once

#include "CoreMinimal.h"
#include "FSMAdditiveState.generated.h"

/**
 * 
 */
UCLASS( Abstract, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), DefaultToInstanced )
class QUANTOMB_API UFSMAdditiveState : public UObject
{
public:
	GENERATED_BODY()

	// Flag to track if this additive state is currently active
	bool bIsActive = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString StateName;
	
	// Is called once when the state machine initializes all states and their transitions.
	// Cast the UObject to the desired class here to avoid unnecessarily casting each frame.
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category="State")
	void Init(UObject* Entity);
	virtual void Init_Implementation(UObject* Entity);

	// Function that returns either true or false. Used to decide whether a state should transition or not.
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category="Transitions")
	bool GuardFunction(UObject* Entity);
	virtual bool GuardFunction_Implementation(UObject* Entity);

	// Called once when entering the state
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category="State")
	void Enter(UObject* Entity);
	virtual void Enter_Implementation(UObject* Entity);

	// Called once when exiting the state
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category="State")
	void Exit(UObject* Entity);
	virtual void Exit_Implementation(UObject* Entity);

	// Called every frame to trigger functionality that should take place in this state.
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category="State")
	void Tick(UObject* Entity);
	virtual void Tick_Implementation(UObject* Entity);
};
