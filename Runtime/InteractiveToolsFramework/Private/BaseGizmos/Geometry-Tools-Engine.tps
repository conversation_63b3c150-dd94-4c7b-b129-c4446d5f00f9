<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Geometry Tools Engine</Name>
  <Location>//UE5/Main/Engine/Source/Runtime/Experimental/InteractiveToolsFramework/Private/BaseGizmos/</Location>
  <Function>Algorithms for ray-primitive intersection, including ray-cylinder, ray-cone, ray-box, and ray-sphere intersection.  (Note: algorithms in this library have also already been approved for use in the GeometryProcessing plugin, specifically in \\UE5\Main\Engine\Plugins\Experimental\GeometryProcessing\Source\GeometricObjects\Public\Intersection\IntersectionUtil.h)</Function>
  <Eula>https://www.geometrictools.com/License/Boost/LICENSE_1_0.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>